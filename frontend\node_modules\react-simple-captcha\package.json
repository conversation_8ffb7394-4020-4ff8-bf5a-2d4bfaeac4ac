{"name": "react-simple-captcha", "version": "9.3.1", "description": "A very simple,powerful and highly customizable captcha for ReactJS", "main": "react-simple-captcha.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/masroorejaz/react-simple-captcha.git"}, "keywords": ["react", "reactjs-<PERSON><PERSON>a", "<PERSON><PERSON>a", "react-captcha", "react-simple-captcha"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/masroorejaz/react-simple-captcha/issues"}, "homepage": "https://scriptse.com/blog/add-captcha-in-reactjs-application/react-simple-captcha-demo/", "dependencies": {"react-html-parser": "^2.0.2"}, "devDependencies": {"react": "^17.1.0", "react-dom": "^17.1.0"}, "peerDependencies": {"react": "*"}}