import isEmptyTextNode from './utils/isEmptyTextNode';
import convertNodeToElement from './convertNodeToElement';

/**
 * Processes the nodes generated by htmlparser2 and convert them all into React elements
 *
 * @param {Object[]} nodes List of nodes to process
 * @param {Function} transform Transform function to optionally apply to nodes
 * @returns {React.Element[]} The list of processed React elements
 */
export default function processNodes(nodes, transform) {

  return nodes
    .filter(node => !isEmptyTextNode(node))
    .map((node, index) => {

      // return the result of the transform function if applicable
      let transformed;
      if (typeof transform === 'function') {
        transformed = transform(node, index);
        if (transformed === null || !!transformed) {
          return transformed;
        }
      }

      // otherwise convert the node as standard
      return convertNodeToElement(node, index, transform);

    });

}
