{"name": "react-html-parser", "version": "2.0.2", "description": "Parse HTML into React components", "main": "lib/index.js", "jsnext:main": "src/index.js", "scripts": {"clean": "rimraf coverage dist lib", "check": "npm run lint && npm run test", "test": "karma start --single-run", "test:watch": "karma start", "lint": "eslint src test demo/src", "build": "npm run build:lib && npm run build:umd && npm run build:umd:min", "build:lib": "rimraf lib && babel ./src -d lib", "build:umd": "webpack src/index.js dist/react-html-parser.js --config webpack.config.development.js", "build:umd:min": "webpack src/index.js -p dist/react-html-parser.min.js --config webpack.config.production.js", "prerelease": "npm run clean && npm run check", "release": "npm run build", "postrelease": "git push --follow-tags && npm publish", "release:patch": "npm version patch && npm run release", "release:minor": "npm version minor && npm run release", "release:major": "npm version major && npm run release", "demo:clean": "rimraf demo/dist", "demo:dev": "cd demo && node ./server.js", "demo:build": "cd demo && npm run build", "demo:push": "cd demo/dist && git init && git commit --allow-empty -m 'update demo' && git checkout -b gh-pages && git add . && git commit -am 'update demo' && <NAME_EMAIL>:wrakky/react-html-parser gh-pages --force", "demo:publish": "npm run demo:clean && npm run demo:build && npm run demo:push"}, "repository": {"type": "git", "url": "https://github.com/wrakky/react-html-parser.git"}, "keywords": ["react", "html", "htmlparser", "htmlparser2", "inner html", "dangerouslySetInnerHTML"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"babel": "^6.5.2", "babel-cli": "^6.8.0", "babel-core": "^6.8.0", "babel-loader": "^6.2.4", "babel-plugin-transform-object-assign": "^6.8.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-es2015": "^6.6.0", "babel-preset-react": "^6.5.0", "coveralls": "^2.11.9", "eslint": "^2.10.1", "eslint-plugin-react": "^5.1.1", "inject-loader": "^2.0.1", "isparta-loader": "^2.0.0", "jasmine-core": "^2.4.1", "json-loader": "^0.5.4", "karma": "^0.13.22", "karma-coverage": "^1.0.0", "karma-es6-shim": "^1.0.0", "karma-jasmine": "^1.0.2", "karma-phantomjs-launcher": "^1.0.0", "karma-phantomjs-shim": "^1.4.0", "karma-spec-reporter": "^0.0.26", "karma-webpack": "^1.7.0", "phantomjs-prebuilt": "^2.1.7", "react": "^16.0.0", "react-dom": "^16.0.0", "rimraf": "^2.5.2", "webpack": "^1.13.0"}, "dependencies": {"htmlparser2": "^3.9.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0 || ^16.0.0-0"}}