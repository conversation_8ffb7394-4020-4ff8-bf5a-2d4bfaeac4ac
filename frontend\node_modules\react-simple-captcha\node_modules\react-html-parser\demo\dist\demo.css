body,html{background:#f5f5f5;margin:0;height:100%}body{padding:1rem;color:#000;font-size:1rem;font-family:Proxima Nova,proxima-nova,sans-serif;font-weight:400;line-height:1.6;letter-spacing:.8px}h1{line-height:1;margin:0}*{box-sizing:border-box}#root{height:100%}#app{height:100%;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column}#app,main{display:-webkit-box;display:-ms-flexbox;display:flex}main{overflow:hidden}main,main>div{-webkit-box-flex:1;-ms-flex:1;flex:1}main>div{margin:0 1rem 0 0}header h1{padding:0 0 1rem}header a{float:right;text-decoration:none;color:#c00;line-height:32px}header a svg{margin-right:5px;position:relative;top:2px}header a svg #github{fill:#c00}header a:hover{color:#900}header a:hover #github{fill:#900}#editor{border:1px solid #aaa;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column}#editor #HTML_EDITOR{-webkit-box-flex:1;-ms-flex:1;flex:1}#editor .presets{padding:5px 10px;border-bottom:1px solid #aaa;display:-webkit-box;display:-ms-flexbox;display:flex}#editor .presets ul{margin:0;padding:0;-webkit-box-flex:1;-ms-flex:1;flex:1;text-align:right}#editor .presets li{margin:0;padding:0 10px;list-style-type:none;display:inline;border-right:1px solid #aaa}#editor .presets li:last-of-type{border:none;padding-right:0}#html{background:#fff;border:1px solid #aaa;padding:.5rem;font-size:.9rem;overflow-y:scroll}#html h1,#html h2,#html h3,#html h4,#html h5,#html p,#html ul{margin:.5rem 0}