{"name": "withStartIndices adds correct startIndex properties", "options": {"withStartIndices": true}, "streaming": false, "html": "<!DOCTYPE html> <html> <title>The Title</title> <body class='foo'>Hello world <p></p></body> <!-- the comment --> </html> ", "expected": [{"startIndex": 0, "name": "!doctype", "data": "!DOCTYPE html", "type": "directive"}, {"type": "text", "data": " "}, {"startIndex": 16, "type": "tag", "name": "html", "attribs": {}, "parent": null, "children": [{"startIndex": 22, "type": "text", "data": " "}, {"startIndex": 23, "type": "tag", "name": "title", "attribs": {}, "children": [{"startIndex": 30, "data": "The Title", "type": "text"}]}, {"startIndex": 47, "type": "text", "data": " "}, {"startIndex": 48, "type": "tag", "name": "body", "attribs": {"class": "foo"}, "children": [{"startIndex": 66, "data": "Hello world ", "type": "text"}, {"startIndex": 78, "type": "tag", "name": "p", "attribs": {}, "children": []}]}, {"startIndex": 92, "type": "text", "data": " "}, {"startIndex": 93, "type": "comment", "data": " the comment "}, {"startIndex": 113, "type": "text", "data": " "}]}]}