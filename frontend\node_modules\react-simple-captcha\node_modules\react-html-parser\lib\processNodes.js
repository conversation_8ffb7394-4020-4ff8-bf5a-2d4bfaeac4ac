'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = processNodes;

var _isEmptyTextNode = require('./utils/isEmptyTextNode');

var _isEmptyTextNode2 = _interopRequireDefault(_isEmptyTextNode);

var _convertNodeToElement = require('./convertNodeToElement');

var _convertNodeToElement2 = _interopRequireDefault(_convertNodeToElement);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

/**
 * Processes the nodes generated by htmlparser2 and convert them all into React elements
 *
 * @param {Object[]} nodes List of nodes to process
 * @param {Function} transform Transform function to optionally apply to nodes
 * @returns {React.Element[]} The list of processed React elements
 */
function processNodes(nodes, transform) {

  return nodes.filter(function (node) {
    return !(0, _isEmptyTextNode2.default)(node);
  }).map(function (node, index) {

    // return the result of the transform function if applicable
    var transformed = void 0;
    if (typeof transform === 'function') {
      transformed = transform(node, index);
      if (transformed === null || !!transformed) {
        return transformed;
      }
    }

    // otherwise convert the node as standard
    return (0, _convertNodeToElement2.default)(node, index, transform);
  });
}