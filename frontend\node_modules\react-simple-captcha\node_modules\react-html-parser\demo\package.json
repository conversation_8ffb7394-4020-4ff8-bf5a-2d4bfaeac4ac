{"name": "react-html-parser-demo", "version": "0.0.0", "description": "Demo for the React HTML Parser library", "main": "index.js", "scripts": {"start": "node server.js", "build": "webpack --config webpack.config.production.js --progress", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "<PERSON>", "license": "MIT", "devDependencies": {"autoprefixer": "^6.3.6", "babel-cli": "^6.8.0", "babel-core": "^6.8.0", "babel-preset-es2015": "^6.6.0", "babel-preset-react": "^6.5.0", "babel-preset-stage-2": "^6.5.0", "css-loader": "^0.23.1", "extract-text-webpack-plugin": "^1.0.1", "html-webpack-plugin": "^2.17.0", "json-loader": "^0.5.4", "node-sass": "^3.7.0", "postcss-loader": "^0.9.1", "raw-loader": "^0.5.1", "react-hot-loader": "^3.0.0-beta.7", "redux-devtools": "^3.3.1", "sass-loader": "^3.2.0", "style-loader": "^0.13.1", "webpack": "^1.13.0", "webpack-dev-server": "^1.14.1"}, "dependencies": {"brace": "^0.8.0", "normalize-scss": "^4.2.1", "prop-types": "^15.6.0", "react": "^16.0.0", "react-ace": "^5.1.2", "react-dom": "^16.0.0", "react-redux": "^5.0.6", "redux": "^3.7.2"}}